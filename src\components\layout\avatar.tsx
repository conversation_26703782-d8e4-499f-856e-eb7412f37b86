import { useAvatar } from "@/components/layout/avatar-provider";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

export default ({ size = "sm" }: { size?: "sm" | "md" | "lg" }) => {
  const cls: Record<typeof size, string> = {
    sm: "size-8",
    md: "size-12",
    lg: "size-full",
  };

  const image = useAvatar().image;

  return (
    <Avatar className={cls[size]}>
      <AvatarImage className={cls[size]} src={image} alt="profile-picture" />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
  );
};
