"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Trash } from "lucide-react";
import { denyRequest } from "@/server/friend";
import { toast } from "../ui/toast";

type FriendData = {
  id: string;
  name: string;
  image: string | null;
  bio: string | null;
  sender: string;
  receiver: string;
};

type FriendsProps = {
  friends: FriendData[];
  onDenyRequest: (senderId: string, receiverId: string) => Promise<boolean>;
  loading: boolean;
};

export default ({ friends, onDenyRequest, loading }: FriendsProps) => {
  if (loading) return <div className="text-center text-muted-foreground">Loading friends...</div>;
  if (friends.length === 0)
    return <div className="text-center text-muted-foreground">No friends yet. Add some friends to get started!</div>;
  return (
    <ul className="flex flex-col gap-2">
      {friends.map((f) => (
        <li key={f.id} className="flex items-center justify-between gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarImage src={f.image || ""} alt={`${f.name}'s profile picture`} />
              <AvatarFallback>{f.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
            </Avatar>
            <span className="truncate font-medium">{f.name}</span>
          </div>
          <div className="flex flex-row gap-1">
            <Button
              variant={"destructive"}
              size={"icon"}
              onClick={() => {
                onDenyRequest(f.sender, f.receiver);
              }}>
              <Trash />
            </Button>
          </div>
        </li>
      ))}
    </ul>
  );
};
